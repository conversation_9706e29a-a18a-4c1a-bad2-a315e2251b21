const fs = require('fs');

const filePath = './page_nodes_2025-03-13_13-52-48.json';
const rawData = fs.readFileSync(filePath, 'utf-8');
const jsonData = JSON.parse(rawData);

jsonData.forEach(entry => {
  const nid = entry.nid || 'unknown_nid';
  const title = entry.title || 'Untitled';
  const company = entry.field_company?.[0]?.value || 'N/A';
  const taxonomy = entry.taxonomy || {};

  const categories = Object.values(taxonomy)
    .filter(t => t.vid === '5' && t.name)
    .map(t => t.name);

  if (categories.length > 0) {
    console.log(`\n[NID: ${nid}] Title: ${title}`);
    console.log(`  Company: ${company}`);
    console.log(`  Categories (vid=5):`);
    categories.forEach(cat => console.log(`    - ${cat}`));
  }
});
