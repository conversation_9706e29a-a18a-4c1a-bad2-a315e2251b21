const fs = require('fs');

// Constants - Comprehensive list of phone number and customer service related terms to exclude
const STOP_WORDS = [
  // Additional requested terms and variations
  'Customer Care No',
  'Customer Care Number',
  'Contact No',
  'Contact Numbers',
  'Toll Free No',
  'Toll Free Number',
  'Toll Free Numbers',
  'Helpline No',
  'Helpline Number',
  'Helpline Numbers',
  'Call Center No',
  'Call Center Number',
  'Call Center Numbers',
  'Customer Care Service',
  'Customer Support No',
  'Customer Support Number',
  'Customer Support Numbers',
  'Customer Helpline No',
  'Customer Helpline Number',
  'Customer Helpline Numbers',
  'Support No',
  'Support Number',
  'Support Numbers',
  'Helpdesk No',
  'Helpdesk Number',
  'Helpdesk Numbers',
  'Enquiry No',
  'Enquiry Number',
  'Enquiry Numbers',
  'Service Center',
  'Service Centers',

  // Short forms and common variations
  'Customer Care',
  'Customer Support',
  'Customer Service',
  'Customer Helpline',
  'Call Center',
  'Call Centre',
  'Contact',
  'Helpline',
  'Helpdesk',
  'Support',
  'Enquiry',
  'Toll Free',
  'Toll-Free',
  'Phone No.',
  'Phone No',
  'Phone Number',
  'Phone Numbers',
  'Tel No.',
  'Tel No',
  'Telephone No.',
  'Telephone No',
  'Telephone Number',
  'Care No.',
  'Care No',
  'Care Number',
  'Care Numbers',
];

const escapeRegex = (str) => str.replace(/[-[\]/{}()*+?.\\^$|]/g, '\\$&');

const STOP_WORDS_REGEX = new RegExp(
  `\\b(?:${STOP_WORDS.map(escapeRegex).join('|')})\\b`,
  'i'
);


const PHONE_FIELDS = {
  field_toll_free_numbers2: 'TOLL_FREE',
  field_all_india_numbers: 'ALL_INDIA',
  field_international_no: 'INTERNATIONAL',
};
const PLATFORM_PATTERNS = {
  WEBSITE: /^(https?:\/\/)?([a-z0-9\-]+\.)+[a-z]{2,}(\/.*)?$/i,
  FACEBOOK: /facebook\.com/i,
  INSTAGRAM: /instagram\.com/i,
  LINKEDIN: /linkedin\.com/i,
  YOUTUBE: /youtube\.com|youtu\.be/i,
  X: /(?:twitter\.com|x\.com)/i,
  APP_LINK: /play\.google\.com|apps\.apple\.com/i,
};
const REGEXES = {
  companyName: new RegExp(
    `^([\\w&@.\\-\\s()]+?)(?=\\s*(?:${STOP_WORDS.map(escapeRegex).join('|')})\\b|\\s+\\d{3,})`,
    'i'
  ),
  phoneSplit: /^([\+()0-9.\-\s]+)[\s\-]*[\(\[]?(.*?)[\)\]]?$/,
  url: /https?:\/\/[^\s"')]+/g,
};
// const now = new Date().toISOString();

const filePath = './page_nodes_2025-03-13_13-52-48.json';
const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

let idCounter = 1;
const generateId = () => idCounter++;

const extractUrls = (obj) => {
  const urls = new Set();
  const walk = (o) => {
    if (Array.isArray(o)) o.forEach(walk);
    else if (typeof o === 'object' && o !== null)
      Object.values(o).forEach(walk);
    else if (typeof o === 'string') {
      const found = o.match(REGEXES.url);
      if (found) found.forEach((u) => urls.add(u));
    }
  };
  walk(obj);
  return [...urls];
};

const cleanNumber = (number) => number.replace(/[\(\[\{][^)\]\}]*$/, '').trim();

const finalOutput = [];

// Improved company name extraction function
const extractCompanyName = (title) => {
  if (!title) return null;

  // First, try the regex approach to stop at stop words or numbers
  const regexMatch = title.match(REGEXES.companyName);
  if (regexMatch && regexMatch[1].trim()) {
    return regexMatch[1].trim();
  }

  // Fallback: remove phone numbers from end (3+ digits) and split on stop words
  const withoutNumbers = title.replace(/\s+[\d\-().]{3,}.*$/, '').trim();
  const splitResult = withoutNumbers.split(STOP_WORDS_REGEX)[0]?.trim();

  // Additional cleanup: handle cases where stop words are concatenated without spaces
  let cleanedResult = splitResult || withoutNumbers;

  // Check for concatenated stop words and remove them
  for (const stopWord of STOP_WORDS) {
    const escapedStopWord = escapeRegex(stopWord);
    const concatenatedRegex = new RegExp(`(.+?)${escapedStopWord}.*$`, 'i');
    const match = cleanedResult.match(concatenatedRegex);
    if (match && match[1].trim()) {
      cleanedResult = match[1].trim();
      break;
    }
  }

  return cleanedResult;
};

jsonData.forEach((entry) => {
  const title = entry.title?.trim() || null;
  const parentCompany = entry.field_company?.[0]?.value?.trim() || null;

  // Use the improved extraction function
  const companyName = extractCompanyName(title);
  const companyId = generateId(companyName, parentCompany);

  // Company info
  const company = {
    id: companyId,
    company_name: companyName,
    parent_company: parentCompany,
    company_email: entry.field_email?.[0]?.value || null,
    // company_logo_url: ('/' + entry.field_company_logo?.[0]?.filepath) || null,  // Updated this line
    company_logo_url: entry.field_company_logo?.[0]?.filepath
      ? '/' + entry.field_company_logo?.[0]?.filepath
      : null, // Updated this line
    company_country: entry.field_country?.[0]?.value || null,
    company_address: entry.field_address?.[0]?.value || null,
    company_website: entry.field_website?.[0]?.url || null,
  };

  // Contact numbers
  const contact = [];
  for (const [field, type] of Object.entries(PHONE_FIELDS)) {
    if (entry[field]) {
      entry[field].forEach((item) => {
        if (item && item.value) {
          const trimmed = item.value.trim();
          const match = trimmed.match(REGEXES.phoneSplit);
          if (match) {
            let number = cleanNumber(match[1].trim().replace(/\s+/g, ' '));
            const description = match[2]?.trim() || null;
            contact.push({
              company_id: companyId,
              contact_type: type,
              number,
              contact_description: description,
              is_whatsapp: /whatsapp/i.test(number + description),
            });
          }
        }
      });
    }
  }

  // Categories
  const taxonomy = entry.taxonomy || {};
  const category = Object.values(taxonomy)
    .filter((t) => t.vid === '5')
    .map((t) => ({
      company_id: companyId,
      //   category_name: companyName,
      category_name: t.name,
    }));

  if (category.length === 0) {
    category.push({
      company_id: companyId,
      category_name: 'Other',
    });
  }

  // Taxonomy terms
  const taxonomyList = Object.values(taxonomy).map((t) => ({
    name: t.name,
    description: '',
    weight: t.weight || null,
    weight_unused: t.weight_unused || null,
    // created_at: now,
    // updated_at: now
  }));

  // Company URLs
  const allUrls = extractUrls(entry);
  const seen = new Set();
  const url = [];

  for (const u of allUrls) {
    for (const [type, regex] of Object.entries(PLATFORM_PATTERNS)) {
      if (!seen.has(type) && regex.test(u)) {
        url.push({
          company_id: companyId,
          url: u,
          url_type: type,
        });
        seen.add(type);
      }
    }
  }

  const rawWebsite = entry.field_website?.[0]?.url;
  if (rawWebsite && !seen.has('WEBSITE')) {
    url.push({
      company_id: companyId,
      url: rawWebsite,
      url_type: 'WEBSITE',
    });
  }

  // Final structured object
  finalOutput.push({
    id: companyId,
    company,
    contact,
    category,
    taxonomy: taxonomyList,
    url,
  });
});

// Save to file
fs.writeFileSync(
  'final_flat_object_list.json',
  JSON.stringify(finalOutput, null, 2)
);
console.log(
  `✅ Exported ${finalOutput.length} structured records to final_flat_object_list.json`
);
