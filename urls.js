const fs = require('fs');

const filePath = './page_nodes_2025-03-13_13-52-48.json';
const rawData = fs.readFileSync(filePath, 'utf-8');
const jsonData = JSON.parse(rawData);

// Platform matchers
const PLATFORM_PATTERNS = {
  WEBSITE: /^(https?:\/\/)?([a-z0-9\-]+\.)+[a-z]{2,}(\/.*)?$/i,
  FACEBOOK: /facebook\.com/i,
  INSTAGRAM: /instagram\.com/i,
  LINKEDIN: /linkedin\.com/i,
  YOUTUBE: /youtube\.com|youtu\.be/i,
  X: /(?:twitter\.com|x\.com)/i,
  APP_LINK: /play\.google\.com|apps\.apple\.com/i
};

// Function to extract URLs from any object
function extractUrlsFromObject(obj) {
  const urls = [];

  function search(o) {
    if (Array.isArray(o)) {
      o.forEach(search);
    } else if (typeof o === 'object' && o !== null) {
      Object.values(o).forEach(search);
    } else if (typeof o === 'string') {
      const matches = o.match(/https?:\/\/[^\s"')]+/g);
      if (matches) urls.push(...matches);
    }
  }

  search(obj);
  return urls;
}

jsonData.forEach(entry => {
  const nid = entry.nid || 'unknown_nid';
  const title = entry.title || 'Untitled';

  const result = {
    WEBSITE: entry.field_website?.[0]?.url || null,
    FACEBOOK: null,
    INSTAGRAM: null,
    LINKEDIN: null,
    YOUTUBE: null,
    X: null,
    APP_LINK: null
  };

  const foundUrls = extractUrlsFromObject(entry);

  foundUrls.forEach(url => {
    for (const [platform, pattern] of Object.entries(PLATFORM_PATTERNS)) {
      if (!result[platform] && pattern.test(url)) {
        result[platform] = url;
      }
    }
  });

  console.count('Total Objects: ')

  // Only show if at least one result is found
  const hasLinks = Object.values(result).some(Boolean);
  if (hasLinks) {
    console.count('Total URLs: ')
    console.log(`\n[NID: ${nid}] Title: ${title}`);
    for (const [platform, url] of Object.entries(result)) {
      if (url) console.log(`  ${platform.padEnd(10)}: ${url}`);
    }
  } else {
    console.log(`\n[NID: ${nid}] Title: ${title}`);
    console.count('No URLs found: ');
  }
});
console.log(jsonData.length);
