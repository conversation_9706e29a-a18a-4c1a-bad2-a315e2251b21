const fs = require('fs');

const filePath = './page_nodes_2025-03-13_13-52-48.json';
const rawData = fs.readFileSync(filePath, 'utf-8');
const jsonData = JSON.parse(rawData);

const results = [];

// Keywords to cut off at (case-insensitive)
const stopWords = ['Customer', 'Support', 'Service', 'Helpline', 'Help', 'Contact', 'Phone', 'Call', 'No.'];

jsonData.forEach(entry => {
  const title = entry.title?.trim() || '';
  const parentCompany = entry.field_company?.[0]?.value?.trim() || 'N/A';

  // Build a regex to stop at keywords or first number
  const keywordRegex = new RegExp(`^([\\w&@.\\-\\s()]+?)(?=\\s+(${stopWords.join('|')})\\b|\\s*\\d)`, 'i');

  let companyName;

  const match = title.match(keywordRegex);
  if (match) {
    companyName = match[1].trim();
  } else {
    // fallback: remove phone numbers from end
    companyName = title.replace(/\d[\d\s\-().]+$/, '').trim();
  }

  results.push({
    title,
    companyName,
    parentCompany
  });
});

// Output
results.forEach(({ title, companyName, parentCompany }) => {
  console.log(`\nTitle         : ${title}`);
  console.log(`Company Name  : ${companyName}`);
  console.log(`Parent Company: ${parentCompany}`);
});
