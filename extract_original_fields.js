const fs = require('fs');

const filePath = './page_nodes_2025-03-13_13-52-48.json';
const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

const fieldsToExtract = [
    'nid',
  'title',
  'field_company',
//   'field_email',
//   'field_company_logo',
//   'field_country',
//   'field_address',
//   'field_website',
//   'field_toll_free_numbers2',
//   'field_all_india_numbers',
//   'field_international_no',
//   'taxonomy'
];

const extracted = jsonData.map(entry => {
  const obj = {};
  fieldsToExtract.forEach(field => {
    if (entry.hasOwnProperty(field)) {
      obj[field] = entry[field];
    }
  });
  return obj;
});

fs.writeFileSync('original_fields_extracted.json', JSON.stringify(extracted, null, 2));
console.log(`✅ Extracted ${extracted.length} entries to original_fields_extracted.json`);
