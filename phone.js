const fs = require('fs');

const filePath = './page_nodes_2025-03-13_13-52-48.json';
const rawData = fs.readFileSync(filePath, 'utf-8');
const jsonData = JSON.parse(rawData);

const targetFields = [
  'field_international_no',
  'field_all_india_numbers',
  'field_toll_free_numbers2'
];

// Regex to extract phone number followed by optional description
const splitRegex = /^([\+()0-9.\-\s]+)[\s\-]*[\(\[]?(.*?)[\)\]]?$/;

jsonData.forEach(entry => {
  const nid = entry.nid || 'unknown_nid';
  const title = entry.title || 'Untitled';
  let hasAnyPhone = false;

  const groupedData = {};

  targetFields.forEach(field => {
    const fieldData = entry[field];
    if (Array.isArray(fieldData)) {
      const records = [];

      fieldData.forEach(item => {
        const value = item?.value;
        if (typeof value === 'string') {
          const trimmed = value.trim();
          const match = trimmed.match(splitRegex);
          if (match) {
            const number = match[1].trim().replace(/\s+/g, ' ').replace(/[\(\)\[\]]+$/, '');
            const description = match[2]?.trim() || '';
            records.push({ number, description });
          }
        }
      });

      if (records.length > 0) {
        groupedData[field] = records;
        hasAnyPhone = true;
      }
    }
  });

  if (hasAnyPhone) {
    console.log(`\n[NID: ${nid}] Title: ${title}`);
    for (const [field, records] of Object.entries(groupedData)) {
      console.log(`  ${field}:`);
      records.forEach(({ number, description }) => {
        console.log(`    - Number: ${number}`);
        if (description) console.log(`      Text  : ${description}`);
      });
    }
  }
});
