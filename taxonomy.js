const fs = require('fs');

const filePath = './page_nodes_2025-03-13_13-52-48.json';
const rawData = fs.readFileSync(filePath, 'utf-8');
const jsonData = JSON.parse(rawData);

const taxonomyMap = new Map(); // Use to deduplicate terms

jsonData.forEach(entry => {
  const taxonomy = entry.taxonomy || {};
  Object.values(taxonomy).forEach(term => {
    const id = term.tid;
    if (!taxonomyMap.has(id)) {
      taxonomyMap.set(id, {
        name: term.name?.trim() || '',
        description: '',
        weight: term.weight || '',
        weight_unused: term.weight_unused || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }
  });
});

// Print formatted output
console.log(`Extracted ${taxonomyMap.size} taxonomy terms:\n`);
taxonomyMap.forEach(term => {
  console.log(JSON.stringify(term, null, 2));
});
