const fs = require('fs');

// Read the input JSON file
const filePath = './page_nodes_2025-03-13_13-52-48.json';
const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

// Map to store unique categories (using Map to maintain insertion order)
const categoriesMap = new Map();

// Extract categories from each entry
jsonData.forEach(entry => {
    if (entry.taxonomy) {
        Object.values(entry.taxonomy).forEach(tax => {
            // Only process categories (vid === '5')
            if (tax.vid === '5') {
                categoriesMap.set(tax.tid, {
                    tid: tax.tid,
                    name: tax.name,
                });
            }
        });
    }
});

// Convert map to array and sort by TID
const uniqueCategories = Array.from(categoriesMap.values())
    .sort((a, b) => Number(a.tid) - Number(b.tid));

// Save to a new JSON file
fs.writeFileSync('unique_categories.json', JSON.stringify(uniqueCategories, null, 2));

// Print to console
console.log('Unique Categories List:');
console.log('----------------------');
uniqueCategories.forEach(category => {
    console.log(`TID: ${category.tid} - Category: ${category.name}`);
});

console.log(`\n✅ Found ${uniqueCategories.length} unique categories`);
console.log('✅ Exported categories to unique_categories.json');
