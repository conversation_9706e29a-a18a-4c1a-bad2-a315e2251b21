// const fs = require('fs');

// // Function to read and count JSON array length
// function countJsonArrayLength(filePath) {
//     try {
//         // Read the JSON file
//         const jsonData = fs.readFileSync(filePath, 'utf8');
        
//         // Parse JSON string to object
//         const jsonArray = JSON.parse(jsonData);
        
//         // Check if it's an array
//         if (!Array.isArray(jsonArray)) {
//             throw new Error('The JSON file does not contain an array');
//         }
        
//         // Get the length
//         const length = jsonArray.length;
//         console.log(`Array length: ${length}`);

//         for (let i = 0; i < length; i++) {
//             console.log(jsonArray[i].taxonomy);
            
//             // console.log(JSON.parse(jsonArray[i].data));
//         }

//         return;
        
//     } catch (error) {
//         console.error('Error:', error.message);
//         return -1;
//     }
// }

// // Example usage
// const filePath = './page_nodes_2025-03-13_13-52-48.json';
// countJsonArrayLength(filePath);

const fs = require('fs');
const path = './page_nodes_2025-03-13_13-52-48.json';

// Read the file and parse the JSON
fs.readFile(path, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    return;
  }

  try {
    const pages = JSON.parse(data);
    const categories = new Set();

    pages.forEach(page => {
      if (page.taxonomy) {
        Object.values(page.taxonomy).forEach(term => {
          if (term.vid === '5') {
            categories.add(term.name.trim());
          }
        });
      }
    });

    console.log('Unique Categories with vid = 5:');
    console.log(`Total unique categories: ${categories.size}`);
    console.dir([...categories], { maxArrayLength: 20000 });    
  } catch (parseError) {
    console.error('Error parsing JSON:', parseError);
  }
});
