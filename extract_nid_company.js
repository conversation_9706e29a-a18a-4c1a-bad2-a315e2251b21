const fs = require('fs');

// Read the input JSON file
const filePath = './page_nodes_2025-03-13_13-52-48.json';
const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

// Array to store nid and company name pairs
const nidCompanyList = [];

// Extract nid and company name from each entry
jsonData.forEach((entry) => {
  const title = entry.title?.trim() || null;
  const nid = entry.nid;
  const STOP_WORDS = [
    'Customer',
    'Support',
    'Service',
    'Helpline',
    'Help',
    'Contact',
    'Toll',
    'Phone',
    'Call',
    'No.',
  ];

  const REGEXES = {
    companyName: new RegExp(
      `^([\\w&@.\\-\\s()]+?)(?=\\s+(${STOP_WORDS.join('|')})\\b|\\s*\\d)`,
      'i'
    ),
  };

  const match = title.match(REGEXES.companyName);
  const companyName = match
    ? match[1].trim()
    : title.replace(/\d[\d\s\-().]+$/, '').trim();

  //   if (!nidCompanyList.some((item) => item.nid === nid)) {
  // }
  nidCompanyList.push({
    nid: nid,
    company_name: companyName,
  });
});

// Sort by NID
nidCompanyList.sort((a, b) => Number(a.nid) - Number(b.nid));

// Save to a new JSON file
fs.writeFileSync(
  'nid_company_list.json',
  JSON.stringify(nidCompanyList, null, 2)
);

// Print to console
console.log('NID and Company Name List:');
nidCompanyList.forEach((item) => {
  console.log(`NID: ${item.nid} - Company: ${item.company_name}`);
});
console.log(`Total entries: ${jsonData.length}`);
console.log(
  `\n✅ Exported ${nidCompanyList.length} records to nid_company_list.json`
);
