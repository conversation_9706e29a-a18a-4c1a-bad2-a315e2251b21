const bcrypt = require('bcrypt');

class PasswordEncryption {
    constructor(saltRounds = 10) {
        this.saltRounds = saltRounds;
    }

    // Encrypt password
    async encrypt(password) {
        try {
            const hashedPassword = await bcrypt.hash(password, this.saltRounds);
            return hashedPassword;
        } catch (error) {
            throw new Error('Error encrypting password: ' + error.message);
        }
    }

    // Verify password
    async verify(password, hashedPassword) {
        try {
            const isMatch = await bcrypt.compare(password, hashedPassword);
            return isMatch;
        } catch (error) {
            throw new Error('Error verifying password: ' + error.message);
        }
    }
}

// Example usage
async function example() {
    const passwordEncryptor = new PasswordEncryption();
    
    const password = 'admin@123';
    
    try {
        // Encrypt password
        const hashedPassword = await passwordEncryptor.encrypt(password);
        console.log('Encrypted password:', hashedPassword);
        
        // Verify password
        const isMatch = await passwordEncryptor.verify(password, hashedPassword);
        console.log('Password verification:', isMatch);
    } catch (error) {
        console.error(error.message);
    }
}

module.exports = PasswordEncryption;

// Run example if this file is run directly
if (require.main === module) {
    example();
}